import requests
import json
import os
from getpass import getpass  # 用于在终端中安全地输入密码


def login_and_get_token(session, username, password):
    """
    登录网站并获取认证Token。
    """
    login_url = "https://csadmin.ztgame.com/api/api/login/"
    login_payload = {
        "username": username,
        "password": password,
        "captcha": ""
    }
    headers = {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36',
        'Origin': 'https://csadmin.ztgame.com',
        'Referer': 'https://csadmin.ztgame.com/'
    }

    print(f"正在使用用户名 '{username}' 尝试登录...")
    try:
        response = session.post(login_url, headers=headers, data=json.dumps(login_payload), timeout=20)

        if response.status_code == 200:
            response_data = response.json()
            access_token = response_data.get('data', {}).get('access')

            if access_token:
                print("登录成功，已获取Access Token！")
                return access_token
            else:
                msg = response_data.get("msg", "未知错误")
                print(f"登录请求成功，但未能获取Token。服务器消息: {msg}")
                return None
        else:
            print(f"登录失败。HTTP状态码: {response.status_code}")
            print("响应内容:", response.text)
            return None

    except requests.exceptions.RequestException as e:
        print(f"登录请求时发生网络错误: {e}")
        return None


def get_account_info_by_id(session, player_id: str, token: str):
    """
    【已更新】携带Token，根据ID获取账号信息。
    """
    # 1. 使用您提供的精确查询URL
    search_api_url = 'https://csadmin.ztgame.com/api/api/czrGmt/fetch_user_info'

    # 2. 【关键更新】根据您的日志，Authorization头需要以 "JWT " 开头
    headers = {
        'Authorization': f'JWT {token}',
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36',
    }

    # 3. 【关键更新】根据您的日志，构造正确的请求体
    # 注意：playerId需要是整数类型，我们用 int() 进行转换
    search_payload = {
        "account": "",
        "playerId": int(player_id)
    }

    print(f"正在查询 Player ID: {player_id}...")
    try:
        response = session.post(search_api_url, headers=headers, data=json.dumps(search_payload), timeout=15)

        if response.status_code == 200:
            response_data = response.json()
            # 4. 【关键更新】根据您的日志，提取 user_info
            if response_data.get('code') == 2000 and 'data' in response_data:
                return response_data['data'].get('user_info')
            else:
                print(f"查询ID '{player_id}' 成功，但响应格式不符或无数据。消息: {response_data.get('msg')}")
                return None
        else:
            print(f"查询ID '{player_id}' 失败。状态码: {response.status_code}")
            print("响应内容:", response.text)
            return None

    except requests.exceptions.RequestException as e:
        print(f"ID查询请求时发生网络错误: {e}")
        return None
    except (ValueError, TypeError):
        print(f"错误: Player ID '{player_id}' 不是一个有效的数字。")
        return None


# --- 主程序 ---
if __name__ == "__main__":
    # 建议您在修改密码后，将新密码保存在环境变量中，而不是每次都手动输入
    # 例如：os.environ.get('MY_APP_USER')
    # 为了方便，这里我们继续使用运行时输入
    my_username = input("请输入用户名: ")
    my_password = getpass("请输入密码(输入时不可见): ")

    # 创建一个Session对象来贯穿所有请求
    with requests.Session() as session:
        # 1. 登录并获取Token
        access_token = login_and_get_token(session, my_username, my_password)

        if access_token:
            print("\n" + "=" * 40)
            print("准备开始查询账号信息...")

            # ‼️ 您可以在这里修改或扩充您想查询的ID列表
            ids_to_search = ['********', '10002', '10003']

            for user_id in ids_to_search:
                account_info = get_account_info_by_id(session, user_id, access_token)

                if account_info:
                    print(f"--- Player ID '{user_id}' 的查询结果 ---")
                    print(json.dumps(account_info, indent=2, ensure_ascii=False))
                else:
                    print(f"--- 未能获取ID '{user_id}' 的信息 ---")

                print("-" * 20)

    print("\n所有任务完成。")